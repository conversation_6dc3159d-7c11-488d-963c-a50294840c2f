<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NafaPlace Admin - Dashboard Moderne</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.1/font/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 0 20px 20px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem;
            border-radius: 0 20px 0 0;
        }

        .nav-link {
            color: #64748b;
            padding: 0.75rem 1.25rem;
            margin: 0.25rem 0;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            min-height: calc(100vh - 2rem);
        }

        .stats-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .product-table {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .table thead {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .table thead th {
            border: none;
            font-weight: 600;
            padding: 1rem;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background-color: rgba(99, 102, 241, 0.05);
            transform: scale(1.01);
        }

        .badge-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.75rem;
        }

        .btn-modern {
            border-radius: 10px;
            padding: 0.5rem 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .search-box {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(99, 102, 241, 0.2);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
            background: white;
        }

        .filter-select {
            background: rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(99, 102, 241, 0.2);
            border-radius: 12px;
            padding: 0.75rem 1rem;
        }

        .product-image {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            object-fit: cover;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: 16px;
            margin-bottom: 2rem;
        }

        .animate-slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .notification-badge {
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: -5px;
            right: -5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <div class="row g-0">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2">
                <div class="sidebar">
                    <div class="sidebar-header text-center">
                        <h4 class="fw-bold mb-0">
                            <i class="bi bi-shop"></i> NafaPlace
                        </h4>
                        <small class="opacity-75">Admin Dashboard</small>
                    </div>
                    
                    <div class="p-3">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="#dashboard">
                                    <i class="bi bi-speedometer2 me-2"></i> Tableau de bord
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link active" href="#products">
                                    <i class="bi bi-box-seam me-2"></i> Produits
                                    <span class="notification-badge">12</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#users">
                                    <i class="bi bi-people me-2"></i> Utilisateurs
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#orders">
                                    <i class="bi bi-cart3 me-2"></i> Commandes
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#marketing">
                                    <i class="bi bi-megaphone me-2"></i> Marketing
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#delivery">
                                    <i class="bi bi-truck me-2"></i> Livraison
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#analytics">
                                    <i class="bi bi-graph-up me-2"></i> Analytics
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content animate-slide-in">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="fw-bold mb-1">Gestion des Produits</h2>
                                <p class="mb-0 opacity-75">Gérez votre catalogue produits en toute simplicité</p>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-bell fs-5 me-2"></i>
                                    <span class="badge bg-light text-dark">Admin</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-muted mb-1">Total Produits</h6>
                                        <h3 class="fw-bold mb-0">1,247</h3>
                                        <small class="text-success">+12% ce mois</small>
                                    </div>
                                    <div class="fs-2 text-primary">
                                        <i class="bi bi-box"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-muted mb-1">Stock Faible</h6>
                                        <h3 class="fw-bold mb-0">23</h3>
                                        <small class="text-warning">Attention requise</small>
                                    </div>
                                    <div class="fs-2 text-warning">
                                        <i class="bi bi-exclamation-triangle"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-muted mb-1">Chiffre d'Affaires</h6>
                                        <h3 class="fw-bold mb-0">2.5M GNF</h3>
                                        <small class="text-success">+18% ce mois</small>
                                    </div>
                                    <div class="fs-2 text-success">
                                        <i class="bi bi-currency-dollar"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stats-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-muted mb-1">Nouveaux Produits</h6>
                                        <h3 class="fw-bold mb-0">47</h3>
                                        <small class="text-info">Cette semaine</small>
                                    </div>
                                    <div class="fs-2 text-info">
                                        <i class="bi bi-plus-circle"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters and Search -->
                    <div class="product-table">
                        <div class="p-4 border-bottom">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <div class="position-relative">
                                        <input type="text" class="form-control search-box ps-5" placeholder="Rechercher un produit...">
                                        <i class="bi bi-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select filter-select">
                                        <option>Toutes catégories</option>
                                        <option>Mode</option>
                                        <option>Électronique</option>
                                        <option>Maison</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select filter-select">
                                        <option>Tous vendeurs</option>
                                        <option>Vendeur Test</option>
                                        <option>Électronique Mali</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select filter-select">
                                        <option>Tous statuts</option>
                                        <option>Approuvé</option>
                                        <option>En attente</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-primary btn-modern w-100">
                                        <i class="bi bi-plus-lg me-2"></i>Ajouter
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Products Table -->
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Image</th>
                                        <th>Nom du Produit</th>
                                        <th>Catégorie</th>
                                        <th>Vendeur</th>
                                        <th>Prix</th>
                                        <th>Stock</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <img src="https://images.unsplash.com/photo-1542272604-787c3835535d?w=100&h=100&fit=crop&crop=center" alt="Jeans" class="product-image">
                                        </td>
                                        <td>
                                            <div class="fw-semibold">Jeans Noir Premium</div>
                                            <small class="text-muted">Coupe moderne, taille haute</small>
                                        </td>
                                        <td><span class="badge bg-info">Mode</span></td>
                                        <td>Mamadi Camara</td>
                                        <td class="fw-bold">200,000 GNF</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">2</span>
                                                <div class="progress" style="width: 50px; height: 6px;">
                                                    <div class="progress-bar bg-danger" style="width: 20%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-success badge-status">Approuvé</span></td>
                                        <td>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary btn-modern">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger btn-modern">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <img src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=100&h=100&fit=crop&crop=center" alt="Sac" class="product-image">
                                        </td>
                                        <td>
                                            <div class="fw-semibold">Sac Bleu Élégant</div>
                                            <small class="text-muted">Cuir véritable, multi-poches</small>
                                        </td>
                                        <td><span class="badge bg-info">Mode</span></td>
                                        <td>Vendeur 3</td>
                                        <td class="fw-bold">2,000,000 GNF</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">1</span>
                                                <div class="progress" style="width: 50px; height: 6px;">
                                                    <div class="progress-bar bg-warning" style="width: 40%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-success badge-status">Approuvé</span></td>
                                        <td>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary btn-modern">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger btn-modern">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=100&h=100&fit=crop&crop=center" alt="Smartphone" class="product-image">
                                        </td>
                                        <td>
                                            <div class="fw-semibold">Smartphone Portal Admin</div>
                                            <small class="text-muted">Écran 6.7", 128GB, 5G</small>
                                        </td>
                                        <td><span class="badge bg-warning">Électronique</span></td>
                                        <td>Vendeur Test</td>
                                        <td class="fw-bold">12,000 GNF</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">20</span>
                                                <div class="progress" style="width: 50px; height: 6px;">
                                                    <div class="progress-bar bg-success" style="width: 80%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-success badge-status">Approuvé</span></td>
                                        <td>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary btn-modern">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger btn-modern">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=100&h=100&fit=crop&crop=center" alt="Casque" class="product-image">
                                        </td>
                                        <td>
                                            <div class="fw-semibold">Casque Audio Modifié</div>
                                            <small class="text-muted">Sans fil, réduction de bruit</small>
                                        </td>
                                        <td><span class="badge bg-warning">Électronique</span></td>
                                        <td>Vendeur Test</td>
                                        <td class="fw-bold">18,000 GNF</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">30</span>
                                                <div class="progress" style="width: 50px; height: 6px;">
                                                    <div class="progress-bar bg-success" style="width: 90%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-success badge-status">Approuvé</span></td>
                                        <td>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary btn-modern">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger btn-modern">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <img src="https://images.unsplash.com/photo-1588508065123-287b28e013da?w=100&h=100&fit=crop&crop=center" alt="Laptop" class="product-image">
                                        </td>
                                        <td>
                                            <div class="fw-semibold">Ordinateur Portable Simple</div>
                                            <small class="text-muted">Intel i5, 8GB RAM, SSD 256GB</small>
                                        </td>
                                        <td><span class="badge bg-warning">Électronique</span></td>
                                        <td>Vendeur Test</td>
                                        <td class="fw-bold">1,000 GNF</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">10</span>
                                                <div class="progress" style="width: 50px; height: 6px;">
                                                    <div class="progress-bar bg-success" style="width: 60%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-success badge-status">Approuvé</span></td>
                                        <td>
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline-primary btn-modern">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger btn-modern">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="p-4 border-top">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted">
                                    Affichage de 1 à 5 sur 247 produits
                                </div>
                                <nav aria-label="Product pagination">
                                    <ul class="pagination mb-0">
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#">Précédent</a>
                                        </li>
                                        <li class="page-item active">
                                            <a class="page-link" href="#">1</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="#">2</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="#">3</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="#">Suivant</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script>
        // Animation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate-slide-in');
            });
        });

        // Recherche en temps réel
        document.querySelector('.search-box').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const productName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                if (productName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Effet de hover sur les lignes du tableau
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(99, 102, 241, 0.05)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    </script>
</body>
</html>